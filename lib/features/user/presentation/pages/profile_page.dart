import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/providers/theme_provider.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/widgets/gradient_button.dart';
import '../../../../core/widgets/shimmer_loading.dart';
import '../../domain/entities/user_model.dart';
import '../../domain/entities/user_type.dart';
import '../../domain/entities/business_profile.dart';
import '../../domain/entities/political_profile.dart';
import '../../domain/usecases/user_service.dart';
import '../../domain/usecases/user_type_service.dart';
import 'business_parameters_page.dart';
import 'political_parameters_page.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();

  UserModel? _user;
  bool _isLoading = false;
  File? _profileImage;

  // User type related variables
  List<UserType> _userTypes = [];
  String? _selectedUserTypeId;

  @override
  void initState() {
    super.initState();
    _loadUserData();
    _loadUserTypes();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  Future<void> _loadUserData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final userService = context.read<UserService>();
      final user = await userService.getCurrentUser();

      if (user != null) {
        setState(() {
          _user = user;
          _nameController.text = user.name;
          _emailController.text = user.email ?? '';
          _phoneController.text = user.phoneNumber ?? '';
          _selectedUserTypeId = user.userType;
        });
      }
    } catch (e) {
      AppLogger.error('Failed to load user data', e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to load user data')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loadUserTypes() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final userTypeService = context.read<UserTypeService>();

      // Initialize user types if needed
      await userTypeService.initializeUserTypes();

      // Get all user types
      final userTypes = await userTypeService.getAllUserTypes();

      if (mounted) {
        setState(() {
          _userTypes = userTypes;
          _isLoading = false;
        });
      }
    } catch (e) {
      AppLogger.error('Failed to load user types', e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to load user types')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _pickImage() async {
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 70,
      );

      if (pickedFile != null && mounted) {
        setState(() {
          _profileImage = File(pickedFile.path);
        });
      }
    } catch (e) {
      AppLogger.error('Failed to pick image', e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to pick image')),
        );
      }
    }
  }

  Future<void> _saveProfile() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final userService = context.read<UserService>();

      // Upload profile image if selected
      String? photoUrl;
      if (_profileImage != null && _user != null) {
        photoUrl = await userService.uploadProfilePhoto(
          _user!.uid,
          _profileImage!.path,
        );
      }

      // Update user data
      if (_user != null) {
        await userService.updateUserFields(
          uid: _user!.uid,
          name: _nameController.text,
          email: _emailController.text.isNotEmpty ? _emailController.text : null,
          phoneNumber: _phoneController.text.isNotEmpty ? _phoneController.text : null,
          photoUrl: photoUrl,
          isProfileComplete: true,
          userType: _selectedUserTypeId,
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Profile updated successfully')),
          );
        }

        // Reload user data
        await _loadUserData();
      }
      Navigator.pop(context);
    } catch (e) {
      AppLogger.error('Failed to save profile', e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to save profile')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);

    return Scaffold(
      appBar: themeProvider.gradientAppBar(
        title: 'Profile',
      ),
      body: Container(
        decoration: themeProvider.isPremium
            ? BoxDecoration(
                gradient: AppTheme.premiumGoldBlackGradient,
              )
            : BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    AppTheme.backgroundWhite,
                    AppTheme.lightGradientBg,
                  ],
                ),
              ),
        child: _isLoading
            ? const ProfilePageShimmer()
            : _buildProfileForm(),
      ),
    );
  }

  Widget _buildProfileForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Basic profile form
          Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                _buildProfileImage(),
                const SizedBox(height: 24),
                _buildNameField(),
                const SizedBox(height: 16),
                _buildEmailField(),
                const SizedBox(height: 16),
                _buildPhoneField(),
                const SizedBox(height: 16),
                _buildUserTypeDropdown(),

                          // Show business parameters button if user type is businessman
          if (_selectedUserTypeId == 'businessman' && _user != null) ...[
            Padding(
              padding: const EdgeInsets.only(top: 20.0),
              child: _buildBusinessParametersButton(),
            ),
          ],

          // Show political parameters button if user type is politician
          if (_selectedUserTypeId == 'politician' && _user != null) ...[
            Padding(
              padding: const EdgeInsets.only(top: 32.0),
              child: _buildPoliticalParametersButton(),
            ),
          ],
                const SizedBox(height: 32),

                GradientButton(
                  text: 'Save Profile',
                  onPressed: _saveProfile,
                  isLoading: _isLoading,
                ),
              ],
            ),
          ),


          // We'll add political profile form later
        ],
      ),
    );
  }

  Widget _buildProfileImage() {
    final imageRadius = 60.0;

    return GestureDetector(
      onTap: _pickImage,
      child: Stack(
        children: [
          CircleAvatar(
            radius: imageRadius,
            backgroundColor: Colors.grey.shade200,
            backgroundImage: _getProfileImage(),
            child: _getProfileImage() == null
                ? Icon(
                    Icons.person,
                    size: imageRadius,
                    color: Colors.grey.shade400,
                  )
                : null,
          ),
          Positioned(
            bottom: 0,
            right: 0,
            child: Consumer<ThemeProvider>(
              builder: (context, themeProvider, _) => Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: themeProvider.isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.camera_alt,
                  color: themeProvider.isPremium ? AppTheme.premiumBlack : Colors.white,
                  size: 20,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  ImageProvider? _getProfileImage() {
    if (_profileImage != null) {
      return FileImage(_profileImage!);
    } else if (_user?.photoUrl != null) {
      return NetworkImage(_user!.photoUrl!);
    }
    return null;
  }

  Widget _buildNameField() {
    return TextFormField(
      controller: _nameController,
      decoration: const InputDecoration(
        labelText: 'Name',
        hintText: 'Enter your name',
        prefixIcon: Icon(Icons.person),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please enter your name';
        }
        return null;
      },
    );
  }

  Widget _buildEmailField() {
    return TextFormField(
      controller: _emailController,
      decoration: const InputDecoration(
        labelText: 'Email',
        hintText: 'Enter your email',
        prefixIcon: Icon(Icons.email),
      ),
      keyboardType: TextInputType.emailAddress,
      validator: (value) {
        if (value != null && value.isNotEmpty) {
          // Simple email validation
          if (!value.contains('@') || !value.contains('.')) {
            return 'Please enter a valid email';
          }
        }
        return null;
      },
    );
  }

  Widget _buildPhoneField() {
    return TextFormField(
      controller: _phoneController,
      decoration: const InputDecoration(
        labelText: 'Phone Number',
        hintText: 'Enter your phone number',
        prefixIcon: Icon(Icons.phone),
      ),
      keyboardType: TextInputType.phone,
      readOnly: true, // Phone number is set during authentication
      enabled: false,
    );
  }

  Widget _buildUserTypeDropdown() {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isPremium = themeProvider.isPremium;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'User Type',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: isPremium ? AppTheme.premiumGold : Colors.grey[700],
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          decoration: BoxDecoration(
            color: isPremium ? AppTheme.premiumDarkGrey : Colors.grey[100],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isPremium ? AppTheme.premiumGold.withAlpha(77) : Colors.grey[300]!,
              width: 1,
            ),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: _selectedUserTypeId,
              isExpanded: true,
              hint: Text(
                'Select User Type',
                style: TextStyle(
                  color: isPremium ? Colors.grey[400] : Colors.grey[600],
                ),
              ),
              icon: Icon(
                Icons.arrow_drop_down,
                color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
              ),
              dropdownColor: isPremium ? AppTheme.premiumBlack : Colors.white,
              items: _userTypes.map((UserType userType) {
                return DropdownMenuItem<String>(
                  value: userType.id,
                  child: Text(
                    userType.name,
                    style: TextStyle(
                      color: isPremium ? AppTheme.premiumGold : null,
                    ),
                  ),
                );
              }).toList(),
              onChanged: (String? newValue) {
                setState(() {
                  _selectedUserTypeId = newValue;
                });
              },
            ),
          ),
        ),
        if (_userTypes.isEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Text(
              'Loading user types...',
              style: TextStyle(
                fontSize: 12,
                color: isPremium ? Colors.grey[400] : Colors.grey[600],
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildBusinessParametersButton() {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isPremium = themeProvider.isPremium;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section title
        Padding(
          padding: const EdgeInsets.only(bottom: 16.0),
          child: Text(
            'Business Settings',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
            ),
          ),
        ),

        // Business parameters button
        InkWell(
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => BusinessParametersPage(
                  initialProfile: _user!.businessProfile,
                ),
              ),
            ).then((_) => _loadUserData()); // Refresh data when returning
          },
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: isPremium ? AppTheme.premiumDarkGrey.withAlpha(150) : Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isPremium ? AppTheme.premiumGold.withAlpha(100) : Colors.grey.shade300,
              ),
              boxShadow: [
                BoxShadow(
                  color: isPremium
                      ? AppTheme.premiumGold.withAlpha(30)
                      : Colors.black.withAlpha(10),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                // Icon
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: isPremium ? AppTheme.premiumBlack : AppTheme.primaryBlue.withAlpha(20),
                    shape: BoxShape.circle,
                    border: isPremium
                        ? Border.all(color: AppTheme.premiumGold, width: 1)
                        : null,
                  ),
                  child: Icon(
                    Icons.business_center,
                    color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),

                // Text
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Manage Business',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Configure your business details, address, and other parameters',
                        style: TextStyle(
                          fontSize: 14,
                          color: isPremium ? Colors.white70 : Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),

                // Arrow icon
                Icon(
                  Icons.arrow_forward_ios,
                  color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPoliticalParametersButton() {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isPremium = themeProvider.isPremium;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section title
        Padding(
          padding: const EdgeInsets.only(bottom: 16.0),
          child: Text(
            'Political Settings',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
            ),
          ),
        ),

        // Political parameters button
        InkWell(
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => PoliticalParametersPage(
                  initialProfile: _user!.politicalProfile,
                ),
              ),
            ).then((_) => _loadUserData()); // Refresh data when returning
          },
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: isPremium ? AppTheme.premiumDarkGrey.withAlpha(150) : Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isPremium ? AppTheme.premiumGold.withAlpha(100) : Colors.grey.shade300,
              ),
              boxShadow: [
                BoxShadow(
                  color: isPremium
                      ? AppTheme.premiumGold.withAlpha(30)
                      : Colors.black.withAlpha(10),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                // Icon
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: isPremium ? AppTheme.premiumBlack : AppTheme.primaryBlue.withAlpha(20),
                    shape: BoxShape.circle,
                    border: isPremium
                        ? Border.all(color: AppTheme.premiumGold, width: 1)
                        : null,
                  ),
                  child: Icon(
                    Icons.how_to_vote,
                    color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),

                // Text
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Manage Political Profile',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Configure your party, constituency, and other political details',
                        style: TextStyle(
                          fontSize: 14,
                          color: isPremium ? Colors.white70 : Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),

                // Arrow icon
                Icon(
                  Icons.arrow_forward_ios,
                  color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
