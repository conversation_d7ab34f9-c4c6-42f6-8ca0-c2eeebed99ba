import 'dart:math' as math;
import 'package:flutter/material.dart';

/// A collection of attractive loading indicators for the app

/// Pulsing loading indicator with smooth animation
class PulsingLoadingIndicator extends StatefulWidget {
  final Color color;
  final double size;
  final Duration duration;

  const PulsingLoadingIndicator({
    super.key,
    required this.color,
    this.size = 40,
    this.duration = const Duration(milliseconds: 1200),
  });

  @override
  State<PulsingLoadingIndicator> createState() => _PulsingLoadingIndicatorState();
}

class _PulsingLoadingIndicatorState extends State<PulsingLoadingIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
    _controller.repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          width: widget.size,
          height: widget.size,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: widget.color.withValues(alpha: 0.3 + (_animation.value * 0.7)),
            boxShadow: [
              BoxShadow(
                color: widget.color.withValues(alpha: 0.4),
                blurRadius: 10 + (_animation.value * 10),
                spreadRadius: _animation.value * 5,
              ),
            ],
          ),
          child: Center(
            child: Container(
              width: widget.size * 0.4,
              height: widget.size * 0.4,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: widget.color,
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Spinning dots loading indicator
class SpinningDotsLoader extends StatefulWidget {
  final Color color;
  final double size;
  final Duration duration;

  const SpinningDotsLoader({
    super.key,
    required this.color,
    this.size = 40,
    this.duration = const Duration(milliseconds: 1000),
  });

  @override
  State<SpinningDotsLoader> createState() => _SpinningDotsLoaderState();
}

class _SpinningDotsLoaderState extends State<SpinningDotsLoader>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return Transform.rotate(
            angle: _controller.value * 2 * 3.14159,
            child: Stack(
              children: List.generate(8, (index) {
                final angle = (index * 2 * 3.14159) / 8;
                return Transform.translate(
                  offset: Offset(
                    (widget.size / 3) * math.cos(angle),
                    (widget.size / 3) * math.sin(angle),
                  ),
                  child: Container(
                    width: widget.size / 8,
                    height: widget.size / 8,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: widget.color.withValues(
                        alpha: 0.3 + (0.7 * (index / 8)),
                      ),
                    ),
                  ),
                );
              }),
            ),
          );
        },
      ),
    );
  }
}

/// Wave loading indicator
class WaveLoadingIndicator extends StatefulWidget {
  final Color color;
  final double size;
  final Duration duration;

  const WaveLoadingIndicator({
    super.key,
    required this.color,
    this.size = 40,
    this.duration = const Duration(milliseconds: 1500),
  });

  @override
  State<WaveLoadingIndicator> createState() => _WaveLoadingIndicatorState();
}

class _WaveLoadingIndicatorState extends State<WaveLoadingIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size * 2,
      height: widget.size,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: List.generate(4, (index) {
          return AnimatedBuilder(
            animation: _controller,
            builder: (context, child) {
              final delay = index * 0.2;
              final animationValue = (_controller.value + delay) % 1.0;
              final height = widget.size * 0.3 +
                  (widget.size * 0.7 * math.sin(animationValue * 2 * 3.14159));

              return Container(
                width: widget.size / 6,
                height: height,
                decoration: BoxDecoration(
                  color: widget.color,
                  borderRadius: BorderRadius.circular(widget.size / 12),
                ),
              );
            },
          );
        }),
      ),
    );
  }
}

/// Bouncing dots loading indicator
class BouncingDotsLoader extends StatefulWidget {
  final Color color;
  final double size;
  final Duration duration;

  const BouncingDotsLoader({
    super.key,
    required this.color,
    this.size = 40,
    this.duration = const Duration(milliseconds: 1200),
  });

  @override
  State<BouncingDotsLoader> createState() => _BouncingDotsLoaderState();
}

class _BouncingDotsLoaderState extends State<BouncingDotsLoader>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size * 2,
      height: widget.size,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: List.generate(3, (index) {
          return AnimatedBuilder(
            animation: _controller,
            builder: (context, child) {
              final delay = index * 0.3;
              final animationValue = (_controller.value + delay) % 1.0;
              final bounce = math.sin(animationValue * 2 * 3.14159).abs();

              return Transform.translate(
                offset: Offset(0, -bounce * widget.size * 0.3),
                child: Container(
                  width: widget.size / 4,
                  height: widget.size / 4,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: widget.color,
                    boxShadow: [
                      BoxShadow(
                        color: widget.color.withValues(alpha: 0.3),
                        blurRadius: 4,
                        offset: Offset(0, bounce * 2),
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        }),
      ),
    );
  }
}

/// Full screen loading overlay with message
class FullScreenLoader extends StatelessWidget {
  final String message;
  final Color? backgroundColor;
  final Color? loaderColor;
  final bool isPremium;

  const FullScreenLoader({
    super.key,
    this.message = 'Loading...',
    this.backgroundColor,
    this.loaderColor,
    this.isPremium = false,
  });

  @override
  Widget build(BuildContext context) {
    final bgColor = backgroundColor ??
        (isPremium ? Colors.black.withValues(alpha: 0.8) : Colors.white.withValues(alpha: 0.9));
    final loadColor = loaderColor ??
        (isPremium ? const Color(0xFFD4AF37) : Colors.blue);

    return Container(
      color: bgColor,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            WaveLoadingIndicator(
              color: loadColor,
              size: 60,
            ),
            const SizedBox(height: 32),
            Text(
              message,
              style: TextStyle(
                color: isPremium ? const Color(0xFFD4AF37) : Colors.black87,
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Shimmer loading effect for cards
class ShimmerCard extends StatefulWidget {
  final double width;
  final double height;
  final double borderRadius;

  const ShimmerCard({
    super.key,
    required this.width,
    required this.height,
    this.borderRadius = 12,
  });

  @override
  State<ShimmerCard> createState() => _ShimmerCardState();
}

class _ShimmerCardState extends State<ShimmerCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(
      begin: -1.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(widget.borderRadius),
        color: Colors.grey[200],
      ),
      child: AnimatedBuilder(
        animation: _animation,
        builder: (context, child) {
          return Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(widget.borderRadius),
              gradient: LinearGradient(
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
                colors: [
                  Colors.grey[200]!,
                  Colors.grey[100]!,
                  Colors.grey[200]!,
                ],
                stops: [
                  math.max(0.0, _animation.value - 0.3),
                  _animation.value,
                  math.min(1.0, _animation.value + 0.3),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

/// Loading button with animated state
class LoadingButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;
  final Color? backgroundColor;
  final Color? textColor;
  final Color? loadingColor;
  final double borderRadius;
  final EdgeInsetsGeometry padding;

  const LoadingButton({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.backgroundColor,
    this.textColor,
    this.loadingColor,
    this.borderRadius = 12,
    this.padding = const EdgeInsets.symmetric(vertical: 16, horizontal: 32),
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: isLoading ? null : onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: backgroundColor ?? Colors.blue,
        padding: padding,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius),
        ),
      ),
      child: isLoading
          ? SizedBox(
              height: 20,
              width: 20,
              child: SpinningDotsLoader(
                color: loadingColor ?? Colors.white,
                size: 20,
              ),
            )
          : Text(
              text,
              style: TextStyle(
                color: textColor ?? Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
    );
  }
}