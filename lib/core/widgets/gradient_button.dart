import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../theme/app_theme.dart';
import '../providers/theme_provider.dart';
import '../widgets/attractive_loading.dart';

/// A custom button with gradient background
class GradientButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final LinearGradient? gradient;
  final double borderRadius;
  final EdgeInsetsGeometry padding;
  final TextStyle? textStyle;
  final double elevation;
  final Widget? icon;
  final bool isLoading;
  final bool useTheme2;

  const GradientButton({
    Key? key,
    required this.text,
    required this.onPressed,
    this.gradient,
    this.borderRadius = 16.0,
    this.padding = const EdgeInsets.symmetric(vertical: 14, horizontal: 28),
    this.textStyle,
    this.elevation = 4.0,
    this.icon,
    this.isLoading = false,
    this.useTheme2 = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context, listen: true);
    final isPremium = themeProvider.isPremium;

    if (isPremium) {
      // Premium theme button
      return ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          padding: padding,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
            side: const BorderSide(color: AppTheme.premiumGold, width: 1),
          ),
          elevation: elevation,
          shadowColor: AppTheme.premiumGold.withAlpha(77), // 0.3 * 255 = 77
          backgroundColor: AppTheme.premiumBlack,
          foregroundColor: AppTheme.premiumGold,
        ),
        child: isLoading
            ? SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  color: AppTheme.premiumGold,
                  strokeWidth: 3,
                ),
              )
            : _buildButtonContent(isPremium: true),
      );
    } else {
      // Regular theme button
      return ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          padding: EdgeInsets.zero,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
          ),
          elevation: elevation,
          shadowColor: AppTheme.primaryBlue.withAlpha(128), // 0.5 * 255 = 128
          backgroundColor: Colors.transparent,
        ).copyWith(
          backgroundColor: WidgetStateProperty.resolveWith((states) => null),
        ),
        child: Ink(
          decoration: BoxDecoration(
            gradient: isLoading
                ? null
                : gradient ?? (useTheme2 ? AppTheme.accentGradient : AppTheme.primaryGradient),
            borderRadius: BorderRadius.circular(borderRadius),
          ),
          child: Container(
            padding: padding,
            alignment: Alignment.center,
            child: isLoading
                ? const SizedBox(
                    width: 24,
                    height: 24,
                    child: SpinningDotsLoader(
                      color: Colors.white,
                      size: 24,
                    ),
                  )
                : _buildButtonContent(isPremium: false),
          ),
        ),
      );
    }
  }

  Widget _buildButtonContent({bool isPremium = false}) {
    final TextStyle defaultStyle = isPremium
        ? const TextStyle(
            color: AppTheme.premiumGold,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          )
        : AppTheme.buttonText;

    if (icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          icon!,
          const SizedBox(width: 8),
          Flexible(
            child: Text(
              text,
              style: textStyle ?? defaultStyle,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      );
    }

    return Text(
      text,
      style: textStyle ?? defaultStyle,
    );
  }
}

/// A secondary button with gradient border
class GradientBorderButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final LinearGradient? gradient;
  final double borderRadius;
  final EdgeInsetsGeometry padding;
  final TextStyle? textStyle;
  final Widget? icon;
  final bool isLoading;

  const GradientBorderButton({
    Key? key,
    required this.text,
    required this.onPressed,
    this.gradient,
    this.borderRadius = 16.0,
    this.padding = const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
    this.textStyle,
    this.icon,
    this.isLoading = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context, listen: true);
    final isPremium = themeProvider.isPremium;

    if (isPremium) {
      // Premium theme border button
      return Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [AppTheme.premiumGold, AppTheme.premiumDarkGold],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(borderRadius),
        ),
        child: Container(
          margin: const EdgeInsets.all(2), // Border width
          decoration: BoxDecoration(
            color: AppTheme.premiumBlack,
            borderRadius: BorderRadius.circular(borderRadius - 2),
          ),
          child: ElevatedButton(
            onPressed: isLoading ? null : onPressed,
            style: ElevatedButton.styleFrom(
              padding: padding,
              backgroundColor: Colors.transparent,
              shadowColor: Colors.transparent,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(borderRadius - 2),
              ),
            ),
            child: isLoading
                ? SizedBox(
                    width: 24,
                    height: 24,
                    child: SpinningDotsLoader(
                      color: AppTheme.premiumGold,
                      size: 24,
                    ),
                  )
                : _buildButtonContent(isPremium: true),
          ),
        ),
      );
    } else {
      // Regular theme border button
      return Container(
        decoration: BoxDecoration(
          gradient: gradient ?? AppTheme.primaryGradient,
          borderRadius: BorderRadius.circular(borderRadius),
        ),
        child: Container(
          margin: const EdgeInsets.all(2), // Border width
          decoration: BoxDecoration(
            color: AppTheme.backgroundWhite,
            borderRadius: BorderRadius.circular(borderRadius - 2),
          ),
          child: ElevatedButton(
            onPressed: isLoading ? null : onPressed,
            style: ElevatedButton.styleFrom(
              padding: padding,
              backgroundColor: Colors.transparent,
              shadowColor: Colors.transparent,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(borderRadius - 2),
              ),
            ),
            child: isLoading
                ? const SizedBox(
                    width: 24,
                    height: 24,
                    child: SpinningDotsLoader(
                      color: AppTheme.primaryBlue,
                      size: 24,
                    ),
                  )
                : _buildButtonContent(isPremium: false),
          ),
        ),
      );
    }
  }

  Widget _buildButtonContent({bool isPremium = false}) {
    final TextStyle buttonTextStyle = textStyle ??
        (isPremium
            ? const TextStyle(
                color: AppTheme.premiumGold,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              )
            : AppTheme.buttonText.copyWith(color: AppTheme.primaryBlue));

    if (icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          icon!,
          const SizedBox(width: 8),
          Flexible(
            child: Text(
              text,
              style: buttonTextStyle,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      );
    }

    return Text(text, style: buttonTextStyle);
  }
}
