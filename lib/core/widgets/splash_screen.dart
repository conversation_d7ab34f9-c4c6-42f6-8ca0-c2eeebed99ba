import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';
import '../services/local_storage_service.dart';
import '../theme/app_theme.dart';
import 'attractive_loading.dart';

/// A splash screen that displays different logos based on premium status
class SplashScreen extends StatefulWidget {
  final Widget? child;

  const SplashScreen({super.key, this.child});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> with TickerProviderStateMixin {
  bool _showSplash = true;
  late AnimationController _logoController;
  late AnimationController _textController;
  late Animation<double> _logoScale;
  late Animation<double> _logoOpacity;
  late Animation<double> _textOpacity;
  late Animation<Offset> _textSlide;

  @override
  void initState() {
    super.initState();

    // Initialize animation controllers
    _logoController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _textController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // Logo animations
    _logoScale = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: Curves.elasticOut,
    ));

    _logoOpacity = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeIn),
    ));

    // Text animations
    _textOpacity = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: Curves.easeIn,
    ));

    _textSlide = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: Curves.easeOutCubic,
    ));

    // Start animations
    _logoController.forward();
    Future.delayed(const Duration(milliseconds: 400), () {
      if (mounted) _textController.forward();
    });

    // Start a timer to hide the splash screen after 2.5 seconds
    Future.delayed(const Duration(milliseconds: 2500), () {
      if (mounted) {
        setState(() {
          _showSplash = false;
        });
      }
    });
  }

  @override
  void dispose() {
    _logoController.dispose();
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isPremium = themeProvider.isPremium;

    // Save premium status to local storage during splash screen
    LocalStorageService.savePremiumStatus(isPremium);

    // If splash screen should not be shown, return the child widget
    if (!_showSplash) {
      return widget.child ?? const SizedBox.shrink();
    }

    // Otherwise, show the enhanced splash screen
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: isPremium
              ? AppTheme.premiumGoldBlackGradient
              : AppTheme.primaryGradient,
        ),
        child: Stack(
          children: [
            // Animated background particles
            ...List.generate(20, (index) => _buildFloatingParticle(index, isPremium)),

            // Main content
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Animated logo
                  AnimatedBuilder(
                    animation: _logoController,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: _logoScale.value,
                        child: Opacity(
                          opacity: _logoOpacity.value,
                          child: Container(
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: isPremium
                                      ? AppTheme.premiumGold.withValues(alpha: 0.3)
                                      : AppTheme.primaryBlue.withValues(alpha: 0.3),
                                  blurRadius: 20,
                                  spreadRadius: 5,
                                ),
                              ],
                            ),
                            child: Image.asset(
                              isPremium ? 'assets/icons/premium_logo.png' : 'assets/icons/logo.png',
                              width: 180,
                              height: 180,
                            ),
                          ),
                        ),
                      );
                    },
                  ),

                  const SizedBox(height: 32),

                  // Animated app name
                  AnimatedBuilder(
                    animation: _textController,
                    builder: (context, child) {
                      return SlideTransition(
                        position: _textSlide,
                        child: FadeTransition(
                          opacity: _textOpacity,
                          child: Text(
                            'QuickPosters',
                            style: TextStyle(
                              fontSize: 28,
                              fontWeight: FontWeight.bold,
                              color: isPremium ? AppTheme.premiumGold : Colors.white,
                              letterSpacing: 2.0,
                              shadows: [
                                Shadow(
                                  color: Colors.black.withValues(alpha: 0.3),
                                  offset: const Offset(0, 2),
                                  blurRadius: 4,
                                ),
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  ),

                  const SizedBox(height: 16),

                  // Animated tagline
                  AnimatedBuilder(
                    animation: _textController,
                    builder: (context, child) {
                      return FadeTransition(
                        opacity: _textOpacity,
                        child: Text(
                          'Create Beautiful Posters',
                          style: TextStyle(
                            fontSize: 16,
                            color: isPremium
                                ? AppTheme.premiumLightGold.withValues(alpha: 0.8)
                                : Colors.white.withValues(alpha: 0.8),
                            letterSpacing: 1.0,
                          ),
                        ),
                      );
                    },
                  ),

                  const SizedBox(height: 48),

                  // Enhanced loading indicator
                  PulsingLoadingIndicator(
                    color: isPremium ? AppTheme.premiumGold : Colors.white,
                    size: 40,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFloatingParticle(int index, bool isPremium) {
    final random = (index * 123) % 100;
    final size = 2.0 + (random % 4);
    final left = (random * 3.7) % MediaQuery.of(context).size.width;
    final animationDuration = 3000 + (random * 20);

    return Positioned(
      left: left,
      child: TweenAnimationBuilder<double>(
        duration: Duration(milliseconds: animationDuration),
        tween: Tween(begin: MediaQuery.of(context).size.height, end: -50),
        builder: (context, value, child) {
          return Transform.translate(
            offset: Offset(0, value),
            child: Container(
              width: size,
              height: size,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isPremium
                    ? AppTheme.premiumGold.withValues(alpha: 0.3)
                    : Colors.white.withValues(alpha: 0.3),
              ),
            ),
          );
        },
        onEnd: () {
          // Restart animation
          if (mounted) {
            setState(() {});
          }
        },
      ),
    );
  }
}
